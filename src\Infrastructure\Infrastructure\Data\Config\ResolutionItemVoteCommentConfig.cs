using Domain.Entities.ResolutionManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.Config
{
    /// <summary>
    /// Entity Framework configuration for ResolutionItemVoteComment entity
    /// Configures table structure, relationships, and constraints
    /// </summary>
    public class ResolutionItemVoteCommentConfig : IEntityTypeConfiguration<ResolutionItemVoteComment>
    {
        public void Configure(EntityTypeBuilder<ResolutionItemVoteComment> builder)
        {
            // Table configuration
            builder.ToTable("ResolutionItemVoteComments");
            
            // Primary key
            builder.HasKey(x => x.Id);
            
            // Properties configuration
            builder.Property(x => x.Comment)
                .IsRequired()
                .HasMaxLength(2000)
                .HasComment("Comment text providing additional information about the item vote");
                
            builder.Property(x => x.ResolutionItemVoteID)
                .IsRequired()
                .HasComment("Foreign key reference to ResolutionItemVote entity");
            
            // Relationships configuration
            builder.HasOne(x => x.ResolutionItemVote)
                .WithMany(v => v.ResolutionItemVoteComments)
                .HasForeignKey(x => x.ResolutionItemVoteID)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_ResolutionItemVoteComments_ItemVotes");
            
            // Indexes for performance
            builder.HasIndex(x => x.ResolutionItemVoteID)
                .HasDatabaseName("IX_ResolutionItemVoteComments_ItemVoteId");
                
            builder.HasIndex(x => x.CreatedAt)
                .HasDatabaseName("IX_ResolutionItemVoteComments_CreatedAt");
            
            // Audit fields configuration (inherited from FullAuditedEntity)
            builder.Property(x => x.CreatedAt)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()");
                
            builder.Property(x => x.UpdatedAt)
                .HasDefaultValueSql("GETUTCDATE()");
                
            builder.Property(x => x.IsDeleted)
                .HasDefaultValue(false);
                
            builder.Property(x => x.DeletedAt)
                .IsRequired(false);
            
            // Soft delete filter
            builder.HasQueryFilter(x => !x.IsDeleted.HasValue || !x.IsDeleted.Value);
        }
    }
}
