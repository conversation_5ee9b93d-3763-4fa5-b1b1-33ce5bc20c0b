﻿using Abstraction.Contracts.Logger;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using AutoMapper;
using Domain.Entities;
using Abstraction.Contracts.Repository;

namespace Application.Features.ResolutionMemberVotes.Commands.Edit
{
    public class EditResolutionMemberVoteCommandHandler : BaseResponse<PERSON><PERSON><PERSON>, ICommandHandler<EditResolutionMemberVoteCommand, BaseResponse<string>>
    {

        #region Fileds
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _Repository;
        private readonly IMapper _mapper;
        #endregion

        #region Constructors
        public EditResolutionMemberVoteCommandHandler(IRepositoryManager Repository, IMapper mapper, ILoggerManager logger)
        {
            _logger = logger;
            _Repository = Repository;
            _mapper = mapper;
        }
        #endregion

        #region Handle Functions
        public async Task<BaseResponse<string>> Handle(EditResolutionMemberVoteCommand request, CancellationToken cancellationToken)
        {
            try
            {
                if (request == null)
                    return BadRequest<string>("the request can't be blank");
                var originalEntity = await _Repository.ResolutionMemberVotes.GetByIdAsync<ResolutionMemberVote>(request.Id, true);
                _mapper.Map(request, originalEntity);
                var status = await _Repository.ResolutionMemberVotes.UpdateAsync(originalEntity);
                if (!status)
                    return BadRequest<string>("Update Operation Failed.");
                return Success("Update Operation Successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error: in EditResolutionMemberVoteCommand");
                return ServerError<string>(ex.Message);
            }
        }

        #endregion

    }
}
