using Abstraction.Base.Dto;
using Domain.Entities.ResolutionManagement;
using System.ComponentModel.DataAnnotations;

namespace Infrastructure.Dto.ResolutionVoting
{
    /// <summary>
    /// Data Transfer Object for ResolutionMemberVote entity
    /// Contains properties for resolution member vote management operations
    /// Follows the established DTO pattern in the project
    /// Excludes audit fields as they are handled automatically by the system
    /// </summary>
    public record ResolutionMemberVoteDto : BaseDto
    {
        /// <summary>
        /// Resolution identifier that this vote belongs to
        /// Required field for API operations
        /// </summary>
        [Display(Name = "Resolution ID")]
        public int ResolutionId { get; set; }

        /// <summary>
        /// Board member identifier who cast this vote
        /// Required field for API operations
        /// </summary>
        [Display(Name = "Board Member ID")]
        public int BoardMemberID { get; set; }

        /// <summary>
        /// The vote result (NotVotedYet = 0, Accept = 1, Reject = 2)
        /// Represents the member's voting decision
        /// </summary>
        [Display(Name = "Vote Result")]
        public VoteResult VoteResult { get; set; } = VoteResult.NotVotedYet;

        /// <summary>
        /// Indicates whether the member has voted
        /// Computed property for display purposes
        /// </summary>
        public bool HasVoted => VoteResult != VoteResult.NotVotedYet;

    }
}
