using FluentValidation;
using Microsoft.Extensions.Localization;
using Resources;

namespace Infrastructure.Dto.ResolutionVoting
{
    /// <summary>
    /// FluentValidation class for ResolutionItemVoteDto
    /// Provides validation rules with localized error messages
    /// Follows the established validation pattern in the project
    /// </summary>
    public class ResolutionItemVoteValidation : AbstractValidator<ResolutionItemVoteDto>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;

        public ResolutionItemVoteValidation(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;
            ApplyValidationRules();
        }

        public void ApplyValidationRules()
        {
            RuleFor(x => x.ResolutionItemID)
                .NotEmpty().WithMessage(_localizer["MSG001"]) // "Field is required"
                .GreaterThan(0).WithMessage(_localizer["MSG002"]); // "Invalid value"

            RuleFor(x => x.ResolutionMemberVoteID)
                .NotEmpty().WithMessage(_localizer["MSG001"]) // "Field is required"
                .GreaterThan(0).WithMessage(_localizer["MSG002"]); // "Invalid value"

            RuleFor(x => x.VoteResult)
                .IsInEnum().WithMessage(_localizer["MSG002"]); // "Invalid value"
        }
    }
}
