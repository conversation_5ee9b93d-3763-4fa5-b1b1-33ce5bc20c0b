﻿using Abstraction.Contracts.Logger;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Domain.Entities.ResolutionManagement;
using AutoMapper;
using Abstraction.Contracts.Repository;



namespace Application.Features.ResolutionMemberVotes.Commands.Delete
{
    public class DeleteResolutionMemberVoteCommandHandler : BaseResponse<PERSON><PERSON><PERSON>, ICommandHandler<DeleteResolutionMemberVoteCommand, BaseResponse<string>>
    {

        #region Fileds
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _Repository;
        private readonly IMapper _mapper;
        #endregion

        #region Constructors
        public DeleteResolutionMemberVoteCommandHandler(IRepositoryManager Repository, IMapper mapper, ILoggerManager logger)
        {
            _logger = logger;
            _Repository = Repository;
            _mapper = mapper;
        }
        #endregion

        #region Handle Functions
        public async Task<BaseResponse<string>> Handle(DeleteResolutionMemberVoteCommand request, CancellationToken cancellationToken)
        {
            try
            {
                if (request == null)
                    return BadRequest<string>("the request can't be blank");
                var entity = await _Repository.ResolutionMemberVotes.GetByIdAsync<ResolutionMemberVote>(request.Id, false);
                var status = await _Repository.ResolutionMemberVotes.DeleteAsync(entity);
                if (!status)
                    return BadRequest<string>("Delete Operation Failed.");
                return Success("Delete Operation Successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error: in EditResolutionMemberVoteCommand");
                return ServerError<string>(ex.Message);
            }
        }

        #endregion

    }
}
