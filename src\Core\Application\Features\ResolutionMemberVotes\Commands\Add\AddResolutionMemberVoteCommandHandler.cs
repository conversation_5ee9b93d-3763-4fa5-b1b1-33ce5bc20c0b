﻿using Abstraction.Contracts.Logger;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using AutoMapper;
using Domain.Entities;
using Abstraction.Contracts.Repository;



namespace Application.Features.ResolutionMemberVotes.Commands.Add
{
    public class AddResolutionMemberVoteCommandHandler : BaseResponse<PERSON><PERSON><PERSON>, ICommandHandler<AddResolutionMemberVoteCommand, BaseResponse<string>>
    {

        #region Fileds
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _Repository;
        private readonly IMapper _mapper;
        #endregion

        #region Constructors
        public AddResolutionMemberVoteCommandHandler(IRepositoryManager Repository, IMapper mapper, ILoggerManager logger)
        {
            _logger = logger;
            _Repository = Repository;
            _mapper = mapper;
        }
        #endregion

        #region Handle Functions
        public async Task<BaseResponse<string>> Handle(AddResolutionMemberVoteCommand request, CancellationToken cancellationToken)
        {
            try
            {
                if (request == null)
                    return BadRequest<string>("the request can't be blank");
                var _resultmapper = _mapper.Map<ResolutionMemberVote>(request);
                var result = await _Repository.ResolutionMemberVotes.AddAsync(_resultmapper);
                if (result is null)
                    return BadRequest<string>("Added Operation Failed.");
                return Success("Added Operation Successfully.");

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error: in AddResolutionMemberVoteCommand");
                return ServerError<string>(ex.Message);
            }
        }

        #endregion


    }
}
