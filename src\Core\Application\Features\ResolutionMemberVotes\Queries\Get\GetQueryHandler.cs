﻿using AutoMapper;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Application.Features.ResolutionMemberVotes.Dtos;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;


namespace Application.Features.ResolutionMemberVotes.Queries.Get
{
    public class GetQueryHandler : BaseResponse<PERSON><PERSON><PERSON>, IQueryHandler<GetQuery, BaseResponse<SingleResolutionMemberVoteResponse>>
    {
        #region Fileds
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _Repository;
        private readonly IMapper _mapper;
        #endregion

        #region Constructor(s)
        public GetQueryHandler(IRepositoryManager Repository, IMapper mapper, ILoggerManager logger)
        {
            _logger = logger;
            _Repository = Repository;
            _mapper = mapper;
        }
        #endregion

        #region Functions
        public async Task<BaseResponse<SingleResolutionMemberVoteResponse>> Handle(GetQuery request, CancellationToken cancellationToken)
        {
            try
            {   
                var result = await _Repository.ResolutionMemberVotes.GetByIdAsync<SingleResolutionMemberVoteResponse>(request.Id, false);
                if (result == null)
                    return NotFound<SingleResolutionMemberVoteResponse>("ResolutionMemberVote with this Id not found!");
                var resultMapper = _mapper.Map<SingleResolutionMemberVoteResponse>(result);
                return Success(resultMapper);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error: in GetResultByIdQuery");
                return ServerError<SingleResolutionMemberVoteResponse>(ex.Message);
            }
        }

        #endregion
    }
}
