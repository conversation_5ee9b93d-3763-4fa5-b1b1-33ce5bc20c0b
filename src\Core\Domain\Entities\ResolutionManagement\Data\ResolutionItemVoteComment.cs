using Domain.Entities.Base;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.ResolutionManagement
{
    /// <summary>
    /// Represents a comment associated with a specific resolution item vote
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Allows board members to provide item-specific explanations or notes about their votes
    /// </summary>
    public class ResolutionItemVoteComment : FullAuditedEntity
    {
        /// <summary>
        /// Comment text providing additional information about the item vote
        /// Can be used to explain the reasoning behind the vote decision for this specific item
        /// </summary>
        public string Comment { get; set; } = string.Empty;

        /// <summary>
        /// Resolution item vote identifier that this comment belongs to
        /// Foreign key reference to ResolutionItemVote entity
        /// </summary>
        public int ResolutionItemVoteID { get; set; }

        /// <summary>
        /// Navigation property to ResolutionItemVote entity
        /// Provides access to the item vote this comment belongs to
        /// </summary>
        [ForeignKey("ResolutionItemVoteID")]
        public virtual ResolutionItemVote ResolutionItemVote { get; set; } = null!;

    }
}
