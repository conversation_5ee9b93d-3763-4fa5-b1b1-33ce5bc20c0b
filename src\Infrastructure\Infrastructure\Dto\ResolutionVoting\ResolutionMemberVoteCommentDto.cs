using Abstraction.Base.Dto;
using System.ComponentModel.DataAnnotations;

namespace Infrastructure.Dto.ResolutionVoting
{
    /// <summary>
    /// Data Transfer Object for ResolutionMemberVoteComment entity
    /// Contains properties for resolution member vote comment management operations
    /// Follows the established DTO pattern in the project
    /// Excludes audit fields as they are handled automatically by the system
    /// </summary>
    public record ResolutionMemberVoteCommentDto : BaseDto
    {
        /// <summary>
        /// Resolution member vote identifier that this comment belongs to
        /// Required field for API operations
        /// </summary>
        [Display(Name = "Resolution Member Vote ID")]
        public int ResolutionMemberVoteID { get; set; }

        /// <summary>
        /// Comment text providing additional information about the vote
        /// Required field for API operations
        /// </summary>
        [Display(Name = "Comment")]
        public string Comment { get; set; } = string.Empty;

       

    }
}
