﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Application.Features.ResolutionMemberVotes.Commands.Edit;
using Application.Features.ResolutionMemberVotes.Commands.Add;
using Application.Features.ResolutionMemberVotes.Queries.List;
using Application.Features.ResolutionMemberVotes.Queries.Get;
using Application.Features.ResolutionMemberVotes.Commands.Delete;
using Presentation.Bases;
using Abstraction.Constants.ModulePermissions;
using Abstraction.Base.Response;
using Microsoft.AspNetCore.Http;
using Abstraction.Common.Wappers;
using Application.Features.ResolutionMemberVotes.Dtos;

namespace Presentation.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ResolutionMemberVoteController : AppControllerBase
    {
        [Authorize(Policy = ResolutionMemberVotePermission.List)]
        [HttpGet("List")]
		[ProducesResponseType(typeof(PaginatedResult<SingleResolutionMemberVoteResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetResolutionMemberVotesPaginatedList([FromQuery] ListQuery query)
        {
            var response = await Mediator.Send(query);
            return Ok(response);
        }

        [Authorize(Policy = ResolutionMemberVotePermission.View)]
        [HttpGet("GetById")]
		[ProducesResponseType(typeof(BaseResponse<SingleResolutionMemberVoteResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetResolutionMemberVotesById([FromRoute] int id)
        {
            var response = await Mediator.Send(new GetQuery() { Id = id });
            return NewResult(response);
        }

        [Authorize(Policy = ResolutionMemberVotePermission.Create)]
		[HttpPost("Add")]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> AddResolutionMemberVote([FromBody] AddResolutionMemberVoteCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [Authorize(Policy = ResolutionMemberVotePermission.Edit)]
        [HttpPut("Edit")]
		[ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> EditResolutionMemberVote([FromBody] EditResolutionMemberVoteCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [Authorize(Policy = ResolutionMemberVotePermission.Delete)]
        [HttpDelete("Delete")]
		[ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteResolutionMemberVote([FromRoute] int id)
        {
            var response = await Mediator.Send(new DeleteResolutionMemberVoteCommand() { Id = id });
            return NewResult(response);
        }



    }
}
