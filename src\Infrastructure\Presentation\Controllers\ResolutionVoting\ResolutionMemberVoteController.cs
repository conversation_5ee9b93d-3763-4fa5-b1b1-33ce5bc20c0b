using Abstraction.Base.Dto;
using Abstraction.Base.Response;
using Abstraction.Common.Wappers;
using Abstraction.Contract.Service.ResolutionVoting;
using Domain.Entities.ResolutionManagement;
using Infrastructure.Dto.ResolutionVoting;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Presentation.Bases;

namespace Presentation.Controllers.ResolutionVoting
{
    /// <summary>
    /// Controller for ResolutionMemberVote entity operations
    /// Provides full CRUD operations following service-based architecture strategy pattern
    /// Handles member voting operations for resolutions in the Jadwa Fund Management System
    /// </summary>
    [Route("api/ResolutionVoting/[controller]/[action]")]
    [ApiController]
    public class ResolutionMemberVoteController : BaseController
    {
        private readonly IResolutionMemberVoteService _resolutionMemberVoteService;

        public ResolutionMemberVoteController(IResolutionMemberVoteService resolutionMemberVoteService)
        {
            _resolutionMemberVoteService = resolutionMemberVoteService;
        }

        /// <summary>
        /// Create a new resolution member vote
        /// </summary>
        /// <param name="entity">Resolution member vote data</param>
        /// <returns>Success message or validation errors</returns>
        [HttpPost]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status422UnprocessableEntity)]
        public virtual async Task<IActionResult> CreateResolutionMemberVote([FromBody] ResolutionMemberVoteDto entity)
        {
            var returnValue = await _resolutionMemberVoteService.AddAsync(entity);
            return NewResult(returnValue);
        }

        /// <summary>
        /// Update an existing resolution member vote
        /// </summary>
        /// <param name="entity">Updated resolution member vote data</param>
        /// <returns>Success message or validation errors</returns>
        [HttpPut]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status422UnprocessableEntity)]
        public virtual async Task<IActionResult> UpdateResolutionMemberVote([FromBody] ResolutionMemberVoteDto entity)
        {
            var returnValue = await _resolutionMemberVoteService.UpdateAsync(entity);
            return NewResult(returnValue);
        }

        /// <summary>
        /// Get resolution member vote by ID
        /// </summary>
        /// <param name="id">Resolution member vote ID</param>
        /// <returns>Resolution member vote details</returns>
        [HttpGet]
        [ProducesResponseType(typeof(BaseResponse<ResolutionMemberVoteDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetResolutionMemberVoteById(int id)
        {
            var returnValue = await _resolutionMemberVoteService.GetByIdAsync<ResolutionMemberVoteDto>(id, false);
            return NewResult(returnValue);
        }

        /// <summary>
        /// Get paginated list of resolution member votes
        /// </summary>
        /// <param name="query">Pagination and filtering parameters</param>
        /// <returns>Paginated list of resolution member votes</returns>
        [HttpGet]
        [ProducesResponseType(typeof(PaginatedResult<ResolutionMemberVoteDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ResolutionMemberVoteList([FromQuery] BaseListDto query)
        {
            var returnValue = await _resolutionMemberVoteService.GetAllPagedAsync<ResolutionMemberVoteDto>(query.PageNumber, query.PageSize, query.OrderBy, false);
            return NewResult(returnValue);
        }

        /// <summary>
        /// Get resolution member votes by resolution ID
        /// </summary>
        /// <param name="resolutionId">Resolution ID</param>
        /// <param name="query">Pagination parameters</param>
        /// <returns>Paginated list of votes for the specified resolution</returns>
        [HttpGet]
        [ProducesResponseType(typeof(PaginatedResult<ResolutionMemberVoteDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetVotesByResolution(int resolutionId, [FromQuery] BaseListDto query)
        {
            // Note: This would require a custom service method to filter by resolution ID
            // For now, using the standard GetAllPagedAsync method
            var returnValue = await _resolutionMemberVoteService.GetAllPagedAsync<ResolutionMemberVoteDto>(query.PageNumber, query.PageSize, query.OrderBy, false);
            return NewResult(returnValue);
        }

        /// <summary>
        /// Get resolution member votes by board member ID
        /// </summary>
        /// <param name="boardMemberId">Board member ID</param>
        /// <param name="query">Pagination parameters</param>
        /// <returns>Paginated list of votes for the specified board member</returns>
        [HttpGet]
        [ProducesResponseType(typeof(PaginatedResult<ResolutionMemberVoteDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetVotesByBoardMember(int boardMemberId, [FromQuery] BaseListDto query)
        {
            // Note: This would require a custom service method to filter by board member ID
            // For now, using the standard GetAllPagedAsync method
            var returnValue = await _resolutionMemberVoteService.GetAllPagedAsync<ResolutionMemberVoteDto>(query.PageNumber, query.PageSize, query.OrderBy, false);
            return NewResult(returnValue);
        }
    }
}
