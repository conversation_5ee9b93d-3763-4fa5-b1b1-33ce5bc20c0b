﻿using AutoMapper;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Abstraction.Common.Wappers;
using Application.Features.ResolutionMemberVotes.Dtos;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;


namespace Application.Features.ResolutionMemberVotes.Queries.List
{
    public class ListQueryHandler : BaseResponseHandler, IQueryHandler<ListQuery, BaseResponse<PaginatedResult<SingleResolutionMemberVoteResponse>>>
    {
        #region Fileds
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _Repository;
        private readonly IMapper _mapper;
        #endregion

        #region Constructor(s)
        public ListQueryHandler(IRepositoryManager Repository, IMapper mapper, ILoggerManager logger)
        {
            _logger = logger;
            _Repository = Repository;
            _mapper = mapper;
        }
        #endregion

        #region Functions
        public async Task<BaseResponse<PaginatedResult<SingleResolutionMemberVoteResponse>>> Handle(ListQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var result = _Repository.ResolutionMemberVotes.GetAll<SingleResolutionMemberVoteResponse>(false);
                if (!result.Any())
                {
                    return EmptyCollection(PaginatedResult<SingleResolutionMemberVoteResponse>.Success(new List<SingleResolutionMemberVoteResponse>(), 0, 0, 0));
                }
                var ResolutionMemberVoteList = await _mapper.ProjectTo<SingleResolutionMemberVoteResponse>(result).ToPaginatedListAsync(request.PageNumber, request.PageSize, request.OrderBy);
                return Success(ResolutionMemberVoteList);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error: in ListQuery");
                return ServerError<PaginatedResult<SingleResolutionMemberVoteResponse>>(ex.Message);
            }
        }
        #endregion
    }
}
