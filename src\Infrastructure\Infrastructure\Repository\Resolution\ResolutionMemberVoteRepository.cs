﻿using Abstraction.Contracts.Repository;
using Infrastructure.Data;
using Abstraction.Contract.Service;



namespace Infrastructure.Repository.Resolution
{
    public class ResolutionMemberVoteRepository : GenericRepository, IResolutionMemberVoteRepository
    {
        public ResolutionMemberVoteRepository(AppDbContext repositoryContext, ICurrentUserService currentUserService)
            : base(repositoryContext, currentUserService)
        {

        }
    }
}
