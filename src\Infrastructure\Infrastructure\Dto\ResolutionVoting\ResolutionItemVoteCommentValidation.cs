using FluentValidation;
using Microsoft.Extensions.Localization;
using Resources;

namespace Infrastructure.Dto.ResolutionVoting
{
    /// <summary>
    /// FluentValidation class for ResolutionItemVoteCommentDto
    /// Provides validation rules with localized error messages
    /// Follows the established validation pattern in the project
    /// </summary>
    public class ResolutionItemVoteCommentValidation : AbstractValidator<ResolutionItemVoteCommentDto>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;

        public ResolutionItemVoteCommentValidation(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;
            ApplyValidationRules();
        }

        public void ApplyValidationRules()
        {
            RuleFor(x => x.Comment)
                .NotEmpty().WithMessage(_localizer["MSG001"]) // "Field is required"
                .NotNull().WithMessage(_localizer["MSG001"]) // "Field is required"
                .MaximumLength(2000).WithMessage(_localizer["MSG003"]); // "Maximum length exceeded"

            RuleFor(x => x.ResolutionItemID)
                .NotEmpty().WithMessage(_localizer["MSG001"]) // "Field is required"
                .GreaterThan(0).WithMessage(_localizer["MSG002"]); // "Invalid value"

            RuleFor(x => x.BoardMemberID)
                .NotEmpty().WithMessage(_localizer["MSG001"]) // "Field is required"
                .GreaterThan(0).WithMessage(_localizer["MSG002"]); // "Invalid value"
        }
    }
}
