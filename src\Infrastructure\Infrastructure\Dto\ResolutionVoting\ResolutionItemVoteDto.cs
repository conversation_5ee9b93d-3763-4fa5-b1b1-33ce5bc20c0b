using Abstraction.Base.Dto;
using Domain.Entities.ResolutionManagement;
using System.ComponentModel.DataAnnotations;

namespace Infrastructure.Dto.ResolutionVoting
{
    /// <summary>
    /// Data Transfer Object for ResolutionItemVote entity
    /// Contains properties for resolution item vote management operations
    /// Follows the established DTO pattern in the project
    /// Excludes audit fields as they are handled automatically by the system
    /// </summary>
    public record ResolutionItemVoteDto : BaseDto
    {
        /// <summary>
        /// Resolution item identifier that this vote is for
        /// Required field for API operations
        /// </summary>
        [Display(Name = "Resolution Item ID")]
        public int ResolutionItemID { get; set; }

        /// <summary>
        /// Resolution member vote identifier that this item vote belongs to
        /// Required field for API operations
        /// </summary>
        [Display(Name = "Resolution Member Vote ID")]
        public int ResolutionMemberVoteID { get; set; }

        /// <summary>
        /// The vote result for this specific resolution item
        /// Uses VoteResult enum (NotVotedYet = 0, Accept = 1, Reject = 2)
        /// </summary>
        [Display(Name = "Vote Result")]
        public VoteResult VoteResult { get; set; } = VoteResult.NotVotedYet;

        /// <summary>
        /// Indicates whether the member has voted on this item
        /// Computed property for display purposes
        /// </summary>
        public bool HasVoted => VoteResult != VoteResult.NotVotedYet;

    }
}
