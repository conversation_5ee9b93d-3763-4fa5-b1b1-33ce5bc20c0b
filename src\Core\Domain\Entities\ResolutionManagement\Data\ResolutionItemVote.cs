using Domain.Entities.Base;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.ResolutionManagement
{
    /// <summary>
    /// Represents a vote cast by a board member on a specific resolution item
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Links individual resolution items to member votes with specific vote results
    /// </summary>
    public class ResolutionItemVote : FullAuditedEntity
    {
        /// <summary>
        /// Resolution item identifier that this vote is for
        /// Foreign key reference to ResolutionItem entity
        /// </summary>
        public int ResolutionItemID { get; set; }

        /// <summary>
        /// Resolution member vote identifier that this item vote belongs to
        /// Foreign key reference to ResolutionMemberVote entity
        /// </summary>
        public int ResolutionMemberVoteID { get; set; }

        /// <summary>
        /// The vote result for this specific resolution item
        /// Uses VoteResult enum (NotVotedYet = 0, Accept = 1, Reject = 2)
        /// </summary>
        public VoteResult VoteResult { get; set; } = VoteResult.NotVotedYet;

        /// <summary>
        /// Navigation property to ResolutionItem entity
        /// Provides access to the resolution item being voted on
        /// </summary>
        [ForeignKey("ResolutionItemID")]
        public virtual ResolutionItem ResolutionItem { get; set; } = null!;

        /// <summary>
        /// Navigation property to ResolutionMemberVote entity
        /// Provides access to the parent member vote
        /// </summary>
        [ForeignKey("ResolutionMemberVoteID")]
        public virtual ResolutionMemberVote ResolutionMemberVote { get; set; } = null!;

        /// <summary>
        /// Collection navigation property to ResolutionItemVoteComment entities
        /// Represents comments specific to this item vote
        /// </summary>
        public virtual ICollection<ResolutionItemVoteComment> ResolutionItemVoteComments { get; set; } = new List<ResolutionItemVoteComment>();

        /// <summary>
        /// Checks if the member has voted on this item (not in NotVotedYet state)
        /// </summary>
        /// <returns>True if member has cast a vote on this item, false otherwise</returns>
        public bool HasVoted()
        {
            return VoteResult != VoteResult.NotVotedYet;
        }
 
    }
}
