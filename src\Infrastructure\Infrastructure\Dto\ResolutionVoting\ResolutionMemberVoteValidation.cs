using FluentValidation;
using Microsoft.Extensions.Localization;
using Resources;

namespace Infrastructure.Dto.ResolutionVoting
{
    /// <summary>
    /// FluentValidation class for ResolutionMemberVoteDto
    /// Provides validation rules with localized error messages
    /// Follows the established validation pattern in the project
    /// </summary>
    public class ResolutionMemberVoteValidation : AbstractValidator<ResolutionMemberVoteDto>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;

        public ResolutionMemberVoteValidation(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;
            ApplyValidationRules();
        }

        public void ApplyValidationRules()
        {
            RuleFor(x => x.ResolutionId)
                .NotEmpty().WithMessage(_localizer["MSG001"]) // "Field is required"
                .GreaterThan(0).WithMessage(_localizer["MSG002"]); // "Invalid value"

            RuleFor(x => x.BoardMemberID)
                .NotEmpty().WithMessage(_localizer["MSG001"]) // "Field is required"
                .GreaterThan(0).WithMessage(_localizer["MSG002"]); // "Invalid value"

            RuleFor(x => x.VoteResult)
                .IsInEnum().WithMessage(_localizer["MSG002"]); // "Invalid value"
        }
    }
}
