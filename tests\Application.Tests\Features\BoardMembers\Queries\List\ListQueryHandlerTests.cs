using Abstraction.Base.Response;
using Abstraction.Common.Wappers;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Application.Features.BoardMembers.Dtos;
using Application.Features.BoardMembers.Queries.List;
using AutoMapper;
using Domain.Entities.FundManagement;
using Microsoft.Extensions.Localization;
using Moq;
using Resources;
using Xunit;

namespace Application.Tests.Features.BoardMembers.Queries.List
{
    public class ListQueryHandlerTests
    {
        private readonly Mock<IRepositoryManager> _mockRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IStringLocalizer<SharedResources>> _mockLocalizer;
        private readonly Mock<ILoggerManager> _mockLogger;
        private readonly ListQueryHandler _handler;

        public ListQueryHandlerTests()
        {
            _mockRepository = new Mock<IRepositoryManager>();
            _mockMapper = new Mock<IMapper>();
            _mockLocalizer = new Mock<IStringLocalizer<SharedResources>>();
            _mockLogger = new Mock<ILoggerManager>();

            _handler = new ListQueryHandler(
                _mockRepository.Object,
                _mockMapper.Object,
                _mockLocalizer.Object,
                _mockLogger.Object
            );

            // Setup common localizer mocks
            _mockLocalizer.Setup(x => x[SharedResourcesKey.AnErrorIsOccurredWhileSavingData])
                .Returns(new LocalizedString(SharedResourcesKey.AnErrorIsOccurredWhileSavingData, "An error occurred while saving data"));
            _mockLocalizer.Setup(x => x[SharedResourcesKey.FundNotFound])
                .Returns(new LocalizedString(SharedResourcesKey.FundNotFound, "Fund not found"));
            _mockLocalizer.Setup(x => x[SharedResourcesKey.NoRecords])
                .Returns(new LocalizedString(SharedResourcesKey.NoRecords, "No records found"));
            _mockLocalizer.Setup(x => x[SharedResourcesKey.SystemErrorSavingData])
                .Returns(new LocalizedString(SharedResourcesKey.SystemErrorSavingData, "System error saving data"));
        }

        [Fact]
        public async Task Handle_RequestIsNull_ReturnsServerError()
        {
            // Arrange
            ListQuery request = null;

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.Succeeded);
            Assert.Equal("An error occurred while saving data", result.Message);
        }

        [Fact]
        public async Task Handle_FundNotFound_ReturnsServerError()
        {
            // Arrange
            var request = new ListQuery { FundId = 1, PageNumber = 1, PageSize = 10 };
            _mockRepository.Setup(r => r.Funds.GetByIdAsync<Fund>(request.FundId, false)).ReturnsAsync((Fund)null);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.Succeeded);
            Assert.Equal("Fund not found", result.Message);
        }

        [Fact]
        public async Task Handle_NoBoardMembers_ReturnsEmptyCollection()
        {
            // Arrange
            var request = new ListQuery { FundId = 1, PageNumber = 1, PageSize = 10 };
            var fund = new Fund { Id = 1 };
            var emptyQueryable = new List<BoardMember>().AsQueryable();

            _mockRepository.Setup(r => r.Funds.GetByIdAsync<Fund>(request.FundId, false)).ReturnsAsync(fund);
            _mockRepository.Setup(r => r.BoardMembers.GetBoardMembersByTypeAsync(request.FundId)).Returns(emptyQueryable);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.True(result.Succeeded);
            Assert.Empty(result.Data);
            Assert.Equal("No records found", result.Message);
        }

        [Fact]
        public async Task Handle_ValidRequest_ReturnsPaginatedBoardMembers()
        {
            // Arrange
            var request = new ListQuery { FundId = 1, PageNumber = 1, PageSize = 10 };
            var fund = new Fund { Id = 1 };
            var boardMembers = new List<BoardMember>
            {
                new BoardMember { Id = 1, FundId = 1, UserId = 1, MemberType = BoardMemberType.Independent }
            }.AsQueryable();

            var mappedDtos = new List<BoardMemberDto>
            {
                new BoardMemberDto { Id = 1, FundId = 1, UserId = 1, MemberType = BoardMemberType.Independent }
            }.AsQueryable();

            _mockRepository.Setup(r => r.Funds.GetByIdAsync<Fund>(request.FundId, false)).ReturnsAsync(fund);
            _mockRepository.Setup(r => r.BoardMembers.GetBoardMembersByTypeAsync(request.FundId)).Returns(boardMembers);
            _mockMapper.Setup(m => m.ProjectTo<BoardMemberDto>(boardMembers)).Returns(mappedDtos);

            // Mock ToPaginatedListAsync extension method if necessary
            // Assuming it's tested separately or using actual implementation

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.True(result.Succeeded);
            Assert.Single(result.Data);
            _mockLogger.Verify(l => l.LogInfo(It.IsAny<string>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ExceptionOccurs_ReturnsServerError()
        {
            // Arrange
            var request = new ListQuery { FundId = 1, PageNumber = 1, PageSize = 10 };
            _mockRepository.Setup(r => r.Funds.GetByIdAsync<Fund>(request.FundId, false)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.Succeeded);
            Assert.Equal("System error saving data", result.Message);
            _mockLogger.Verify(l => l.LogError(It.IsAny<Exception>(), It.IsAny<string>()), Times.Once);
        }
    }
}
