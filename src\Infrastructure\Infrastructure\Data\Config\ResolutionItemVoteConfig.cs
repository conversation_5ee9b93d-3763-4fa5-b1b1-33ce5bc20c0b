using Domain.Entities.ResolutionManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.Config
{
    /// <summary>
    /// Entity Framework configuration for ResolutionItemVote entity
    /// Configures table structure, relationships, and constraints
    /// </summary>
    public class ResolutionItemVoteConfig : IEntityTypeConfiguration<ResolutionItemVote>
    {
        public void Configure(EntityTypeBuilder<ResolutionItemVote> builder)
        {
            // Table configuration
            builder.ToTable("ResolutionItemVotes");
            
            // Primary key
            builder.HasKey(x => x.Id);
            
            // Properties configuration
            builder.Property(x => x.ResolutionItemID)
                .IsRequired()
                .HasComment("Foreign key reference to ResolutionItem entity");
                
            builder.Property(x => x.ResolutionMemberVoteID)
                .IsRequired()
                .HasComment("Foreign key reference to ResolutionMemberVote entity");
                
            builder.Property(x => x.VoteResult)
                .IsRequired()
                .HasConversion<int>()
                .HasDefaultValue(VoteResult.NotVotedYet)
                .HasComment("Vote result for this specific resolution item (NotVotedYet=0, Accept=1, Reject=2)");
            
            // Relationships configuration
            builder.HasOne(x => x.ResolutionItem)
                .WithMany()
                .HasForeignKey(x => x.ResolutionItemID)
                .OnDelete(DeleteBehavior.Restrict)
                .HasConstraintName("FK_ResolutionItemVotes_ResolutionItems");

            builder.HasOne(x => x.ResolutionMemberVote)
                .WithMany(v => v.ResolutionItemVotes)
                .HasForeignKey(x => x.ResolutionMemberVoteID)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_ResolutionItemVotes_MemberVotes");
            
            // Indexes for performance
            builder.HasIndex(x => x.ResolutionItemID)
                .HasDatabaseName("IX_ResolutionItemVotes_ItemId");
                
            builder.HasIndex(x => x.ResolutionMemberVoteID)
                .HasDatabaseName("IX_ResolutionItemVotes_MemberVoteId");
                
            builder.HasIndex(x => new { x.ResolutionItemID, x.ResolutionMemberVoteID })
                .HasFilter("[IsDeleted] = 0")
                .IsUnique()
                .HasDatabaseName("IX_ResolutionItemVotes_Item_MemberVote_Unique");
                
            builder.HasIndex(x => x.VoteResult)
                .HasDatabaseName("IX_ResolutionItemVotes_VoteResult");
            
            // Audit fields configuration (inherited from FullAuditedEntity)
            builder.Property(x => x.CreatedAt)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()");
                
            builder.Property(x => x.UpdatedAt)
                .HasDefaultValueSql("GETUTCDATE()");
                
            builder.Property(x => x.IsDeleted)
                .HasDefaultValue(false);
                
            builder.Property(x => x.DeletedAt)
                .IsRequired(false);
            
            // Soft delete filter
            builder.HasQueryFilter(x => !x.IsDeleted.HasValue || !x.IsDeleted.Value);
        }
    }
}
