using Abstraction.Base.Dto;
using System.ComponentModel.DataAnnotations;

namespace Infrastructure.Dto.ResolutionVoting
{
    /// <summary>
    /// Data Transfer Object for ResolutionItemVoteComment entity
    /// Contains properties for resolution item vote comment management operations
    /// Follows the established DTO pattern in the project
    /// Excludes audit fields as they are handled automatically by the system
    /// </summary>
    public record ResolutionItemVoteCommentDto : BaseDto
    {
        /// <summary>
        /// Comment text providing additional information about the item vote
        /// Required field for API operations
        /// </summary>
        [Display(Name = "Comment")]
        public string Comment { get; set; } = string.Empty;

        /// <summary>
        /// Resolution item identifier that this comment belongs to
        /// Required field for API operations
        /// </summary>
        [Display(Name = "Resolution Item ID")]
        public int ResolutionItemID { get; set; }

        /// <summary>
        /// Board member identifier who made this comment
        /// Required field for API operations
        /// </summary>
        [Display(Name = "Board Member ID")]
        public int BoardMemberID { get; set; }

       
    }
}
