using AutoMapper;
using Domain.Entities.ResolutionManagement;

namespace Infrastructure.Dto.ResolutionVoting
{
    /// <summary>
    /// AutoMapper profile for Resolution Voting entities and DTOs mapping
    /// Configures bidirectional mapping between domain entities and data transfer objects
    /// Automatically registered by the AutoMapper configuration
    /// </summary>
    public class ResolutionVotingProfile : Profile
    {
        /// <summary>
        /// Initializes the mapping configuration for Resolution Voting entities
        /// </summary>
        public ResolutionVotingProfile()
        {
            // ResolutionMemberVote mappings
            CreateMap<ResolutionMemberVote, ResolutionMemberVoteDto>();
            CreateMap<ResolutionMemberVoteDto, ResolutionMemberVote>()
                // Audit fields are handled automatically by AuditableDbContext
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedBy, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedByUser, opt => opt.Ignore())
                // Navigation properties are handled separately
                .ForMember(dest => dest.Resolution, opt => opt.Ignore())
                .ForMember(dest => dest.BoardMember, opt => opt.Ignore())
                .ForMember(dest => dest.ResolutionMemberVoteStatusHistories, opt => opt.Ignore())
                .ForMember(dest => dest.ResolutionMemberVoteComments, opt => opt.Ignore())
                .ForMember(dest => dest.ResolutionItemVotes, opt => opt.Ignore());

            // ResolutionMemberVoteComment mappings
            CreateMap<ResolutionMemberVoteComment, ResolutionMemberVoteCommentDto>();
            CreateMap<ResolutionMemberVoteCommentDto, ResolutionMemberVoteComment>()
                // Audit fields are handled automatically by AuditableDbContext
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedBy, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedByUser, opt => opt.Ignore())
                // Navigation properties are handled separately
                .ForMember(dest => dest.ResolutionMemberVote, opt => opt.Ignore());

            // ResolutionItemVote mappings
            CreateMap<ResolutionItemVote, ResolutionItemVoteDto>();
            CreateMap<ResolutionItemVoteDto, ResolutionItemVote>()
                // Audit fields are handled automatically by AuditableDbContext
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedBy, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedByUser, opt => opt.Ignore())
                // Navigation properties are handled separately
                .ForMember(dest => dest.ResolutionItem, opt => opt.Ignore())
                .ForMember(dest => dest.ResolutionMemberVote, opt => opt.Ignore())
                .ForMember(dest => dest.ResolutionItemVoteComments, opt => opt.Ignore());

            // ResolutionItemVoteComment mappings
            CreateMap<ResolutionItemVoteComment, ResolutionItemVoteCommentDto>();
            CreateMap<ResolutionItemVoteCommentDto, ResolutionItemVoteComment>()
                // Audit fields are handled automatically by AuditableDbContext
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedBy, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedByUser, opt => opt.Ignore())
                // Navigation properties are handled separately
                .ForMember(dest => dest.ResolutionItemVote, opt => opt.Ignore());
        }
    }
}
